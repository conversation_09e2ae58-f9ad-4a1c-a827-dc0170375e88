# Changelog

All notable changes to the Dento project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Comprehensive project documentation and README
- Testing infrastructure with Jest and React Testing Library
- Project structure improvements and standardization
- Additional documentation files (CHANGELOG, CONTRIBUTING)

### Changed
- Improved project organization and file structure
- Enhanced development workflow documentation

### Fixed
- N/A

### Removed
- N/A

## [0.1.0] - 2024-01-XX

### Added
- Initial project setup with Next.js 15 and TypeScript
- Firebase integration (Auth, Firestore, Functions, Storage)
- Patient management system
- Request forms (İstem Formu) functionality
- Admin dashboard and user management
- Authentication system with role-based access
- Email service integration with Brevo
- Responsive UI with Tailwind CSS and DaisyUI
- Custom design system components
- Real-time data synchronization
- File upload and storage capabilities

### Security
- Implemented Firebase security rules
- Role-based access control
- Secure authentication flow
- Data validation with Zod schemas

---

## Template for Future Releases

### Added
- New features

### Changed
- Changes in existing functionality

### Deprecated
- Soon-to-be removed features

### Removed
- Removed features

### Fixed
- Bug fixes

### Security
- Security improvements
