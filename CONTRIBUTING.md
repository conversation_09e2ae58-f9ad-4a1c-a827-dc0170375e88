# Contributing to <PERSON><PERSON>

Thank you for your interest in contributing to <PERSON><PERSON>! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Firebase CLI
- Git

### Development Setup
1. Fork the repository
2. Clone your fork: `git clone <your-fork-url>`
3. Install dependencies: `npm install`
4. Set up environment variables: `cp .env.example .env.local`
5. Start development server: `npm run dev`
6. Start Firebase emulators: `firebase emulators:start`

## 📋 Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow ESLint configuration
- Use Prettier for code formatting
- Write meaningful commit messages
- Add JSDoc comments for complex functions

### Naming Conventions
- **Files**: Use kebab-case for file names (`patient-table.tsx`)
- **Components**: Use PascalCase (`PatientTable`)
- **Functions/Variables**: Use camelCase (`getUserData`)
- **Constants**: Use UPPER_SNAKE_CASE (`API_ENDPOINTS`)
- **Types/Interfaces**: Use PascalCase (`UserData`, `PatientInfo`)

### Component Structure
```tsx
// Import order: React, Next.js, external libraries, internal imports
import React from 'react'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { usePatient } from '@/lib/hooks/usePatient'

interface ComponentProps {
  // Props interface
}

export function Component({ prop }: ComponentProps) {
  // Component implementation
}
```

### Testing
- Write tests for new features and bug fixes
- Use React Testing Library for component tests
- Aim for 70%+ code coverage
- Run tests before submitting: `npm test`

### Git Workflow
1. Create a feature branch: `git checkout -b feature/amazing-feature`
2. Make your changes
3. Write/update tests
4. Commit changes: `git commit -m 'Add amazing feature'`
5. Push to your fork: `git push origin feature/amazing-feature`
6. Create a Pull Request

## 🐛 Bug Reports

When reporting bugs, please include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Screenshots (if applicable)
- Environment details (browser, OS, etc.)

## 💡 Feature Requests

For feature requests:
- Describe the feature clearly
- Explain the use case and benefits
- Consider implementation complexity
- Check if similar features exist

## 📝 Pull Request Process

1. **Before submitting:**
   - Ensure tests pass: `npm test`
   - Run linting: `npm run lint`
   - Update documentation if needed
   - Add/update tests for new features

2. **PR Description:**
   - Clear title and description
   - Link related issues
   - List changes made
   - Include screenshots for UI changes

3. **Review Process:**
   - PRs require at least one review
   - Address feedback promptly
   - Keep PRs focused and small
   - Rebase if needed to keep history clean

## 🏗️ Project Structure

### Directory Guidelines
- `app/` - Next.js App Router pages
- `components/` - Reusable React components
- `lib/` - Utilities, services, and shared logic
- `tests/` - Test files
- `docs/` - Documentation
- `functions/` - Firebase Cloud Functions

### Component Organization
- Group related components in feature directories
- Keep components small and focused
- Use composition over inheritance
- Implement proper error boundaries

## 🔒 Security

- Never commit sensitive data (API keys, passwords)
- Use environment variables for configuration
- Follow Firebase security best practices
- Report security issues privately

## 📚 Documentation

- Update README.md for significant changes
- Document new APIs and components
- Include code examples
- Keep documentation up to date

## 🎯 Code Review Checklist

### For Authors
- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No console.log statements
- [ ] Error handling is implemented
- [ ] Performance considerations addressed

### For Reviewers
- [ ] Code is readable and maintainable
- [ ] Logic is sound and efficient
- [ ] Tests cover edge cases
- [ ] Security implications considered
- [ ] UI/UX is consistent
- [ ] Accessibility requirements met

## 🤝 Community

- Be respectful and inclusive
- Help others learn and grow
- Share knowledge and best practices
- Provide constructive feedback

## 📞 Getting Help

- Check existing issues and documentation
- Ask questions in discussions
- Contact maintainers for urgent issues

Thank you for contributing to Dento! 🦷✨
