'use client';

import { useEffect, useState, useCallback } from 'react';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Loading from '@/components/dentoui/Loading';
import { useAuth } from '@/lib/contexts/AuthContext';
import { IstemFormuData, Patient, User } from '@/types';
import Header from '@/components/dentoui/Header';
import DentoButtonSecondary from '@/components/dentoui/DentoButtonSecondary';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { getPatientsByDoctorId } from '@/lib/services/patientService';
import { useUser } from '@/lib/hooks/useUser';
import IstemFormuStatsCards from '@/components/istem-formu/IstemFormuStatsCards';
import IstemFormuTable from '@/components/istem-formu/IstemFormuTable';
import IstemFormuDetailsModal from '@/components/istem-formu/IstemFormuDetailsModal';
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export default function IstemFormuPage() {
  const router = useRouter();
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('Tüm Durumlar');
  const [istemFormulari, setIstemFormulari] = useState<IstemFormuData[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingForms, setIsLoadingForms] = useState(true);
  const [totalForms, setTotalForms] = useState(0);
  const [pendingFormCount, setPendingFormCount] = useState(0);
  const [completedFormCount, setCompletedFormCount] = useState(0);
  const [thisMonthFormCount, setThisMonthFormCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedForm, setSelectedForm] = useState<IstemFormuData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const ITEMS_PER_PAGE = 10;

  const { user, loading: authLoading } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  const { isAdmin } = useUser();

  // Debounce the search query
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => {
      clearTimeout(timerId);
    };
  }, [searchQuery]);

  // Reset to page 1 when filters change
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery, statusFilter, currentPage]);

  const totalPages = Math.ceil(totalForms / ITEMS_PER_PAGE);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleFormClick = (form: IstemFormuData) => {
    setSelectedForm(form);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedForm(null);
  };

  // Helper function to get patient by ID
  const getPatientById = (patientId: string) => {
    return patients.find(p => p.id === patientId) || null;
  };

  // Helper function to get doctor by ID
  const getDoctorById = (userId: string) => {
    return users.find(u => u.id === userId) || null;
  };

  // Fetch patients and forms
  const fetchData = useCallback(async () => {
    if (!user) return;
    
    try {
      setIsLoadingForms(true);
        
        // Fetch patients
        let fetchedPatients: Patient[] = [];
        if (isAdmin) {
          // Admin fetches all patients across all doctors
          const patientsCollectionRef = collection(db, 'patients');
          const patientsSnapshot = await getDocs(patientsCollectionRef);
          fetchedPatients = patientsSnapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              ...data,
              birthDate: data.birthDate?.toDate ? data.birthDate.toDate() : data.birthDate,
              createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : new Date(),
              updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date()
            } as Patient;
          });
        } else {
          fetchedPatients = await getPatientsByDoctorId(user.uid);
        }
        setPatients(fetchedPatients);

        // Fetch users if admin
        if (isAdmin) {
          const usersCollection = collection(db, 'users');
          const usersSnapshot = await getDocs(usersCollection);
          const fetchedUsers: User[] = usersSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            birthDate: doc.data().birthDate?.toDate() || new Date(),
            createdAt: doc.data().createdAt?.toDate() || new Date(),
            updatedAt: doc.data().updatedAt?.toDate() || new Date()
          })) as User[];
          setUsers(fetchedUsers);
        }
        
                         // Fetch istem forms - if admin, get all forms, otherwise get only user's forms
        let forms: IstemFormuData[] = [];
        
        if (isAdmin) {
          // Admin: get all forms from all users
          const allUsersSnapshot = await getDocs(collection(db, 'users'));
          for (const userDoc of allUsersSnapshot.docs) {
            const userFormsCollection = collection(db, 'users', userDoc.id, 'istem-formu');
            const userFormsQuery = query(userFormsCollection, orderBy('createdAt', 'desc'));
            const userFormsSnapshot = await getDocs(userFormsQuery);
            
            const userForms = userFormsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data(),
              createdAt: doc.data().createdAt?.toDate() || new Date()
            })) as IstemFormuData[];
            
            forms = [...forms, ...userForms];
          }
          
          // Sort all forms by creation date
          forms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        } else {
          // Regular user: get only their forms
          const formsCollection = collection(db, 'users', user.uid, 'istem-formu');
          const formsQuery = query(formsCollection, orderBy('createdAt', 'desc'));
          
          const formsSnapshot = await getDocs(formsQuery);
          forms = formsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date()
          })) as IstemFormuData[];
        }
        
        // Apply filters
        let filteredForms = forms;
        
        if (statusFilter !== 'Tüm Durumlar') {
          filteredForms = filteredForms.filter(form => form.status === statusFilter);
        }
        
        if (debouncedSearchQuery) {
          filteredForms = filteredForms.filter(form => {
            const patient = fetchedPatients.find(p => p.id === form.patientId);
            const patientName = patient ? `${patient.firstName} ${patient.lastName}` : '';
            return patientName.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
                   form.diagnosis.toLowerCase().includes(debouncedSearchQuery.toLowerCase());
          });
        }
        
        setTotalForms(filteredForms.length);

        const pendingCount = forms.filter(f => f.status === 'pending').length;
        const completedCount = forms.filter(f => f.status === 'completed').length;
        const thisMonthCount = forms.filter(f => new Date(f.createdAt).getMonth() === new Date().getMonth()).length;
        
        // Apply pagination
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const paginatedForms = filteredForms.slice(startIndex, startIndex + ITEMS_PER_PAGE);
        
        setIstemFormulari(paginatedForms);
        setPendingFormCount(pendingCount);
        setCompletedFormCount(completedCount);
        setThisMonthFormCount(thisMonthCount);
  
    } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        setIsLoadingForms(false);
      }
    }, [user, isAdmin, currentPage, statusFilter, debouncedSearchQuery]);

  useEffect(() => {
    if (!authLoading) {
      fetchData();
    }
  }, [user, authLoading, fetchData]);

  if (authLoading) {
    return <Loading message="Kimlik doğrulanıyor..." />;
  }

  const breadcrumbs = [
    { label: 'Anasayfa', href: '/dashboard' },
    { label: 'İstem Formu', href: '/dashboard/istem-formu', isActive: true },
  ];

  const headerActions = (
      <DentoButtonSecondary
      onClick={() => router.push('/dashboard/istem-formu/create')}
      icon={<Plus className="w-5 h-5" />}
      bgColor="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
        textColor="text-white"
        iconAnimation="group-hover:rotate-90"
      className="shadow-lg hover:shadow-xl"
      >
      Yeni Form Ekle
      </DentoButtonSecondary>
  );

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 overflow-y-auto ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header
          title="İstem Formları"
          description="Tüm istem formlarını görüntüleyin ve yönetin"
          breadcrumbs={breadcrumbs}
          rightComponent={headerActions}
        />

        {/* Main Content Area */}
        <div className="p-8">
          {/* Statistics Cards */}
          <IstemFormuStatsCards 
            totalFormCount={totalForms} 
            pendingFormCount={pendingFormCount}
            completedFormCount={completedFormCount}
            thisMonthFormCount={thisMonthFormCount}
          />

          {/* Forms Table */}
          <IstemFormuTable
            istemFormulari={istemFormulari}
            patients={patients}
            users={users}
            isLoading={isLoadingForms}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            currentPage={currentPage}
            totalPages={totalPages}
            totalForms={totalForms}
            itemsPerPage={ITEMS_PER_PAGE}
            onPageChange={handlePageChange}
            isAdmin={isAdmin}
            onFormClick={handleFormClick}
            onRefresh={fetchData}
          />
        </div>
      </div>

      {/* Details Modal */}
      <IstemFormuDetailsModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        form={selectedForm}
        patient={selectedForm ? getPatientById(selectedForm.patientId) : null}
        doctor={selectedForm ? getDoctorById(selectedForm.userId) : null}
      />
    </div>
  );
} 