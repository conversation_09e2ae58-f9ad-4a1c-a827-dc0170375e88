'use client';

import Image from 'next/image';
import { IstemFormuData, Patient, User } from '@/types';
import { X, User as UserIcon, AlertCircle, Camera, FileText } from 'lucide-react';
import FullTeethSet from '@/components/dentoui/FullTeethSet';
import { formatDate } from '@/lib/utils';

interface IstemFormuDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  form: IstemFormuData | null;
  patient: Patient | null;
  doctor: User | null;
}

const Section: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode; className?: string }> = ({ icon, title, children, className }) => (
  <div className={`bg-white rounded-xl p-5 border border-gray-200/80 shadow-sm ${className}`}
  >
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center text-gray-500">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
    </div>
    <div>
      {children}
    </div>
  </div>
);

const InfoItem: React.FC<{ label: string; value: React.ReactNode; }> = ({ label, value }) => (
  <div className="py-2 border-b border-gray-100 last:border-b-0">
    <p className="text-sm font-medium text-gray-600 mb-0.5">{label}</p>
    <div className="text-sm text-gray-800">{value}</div>
  </div>
);


// Helper function to get status badge
const getStatusBadge = (status: string) => {
  const statusConfig = {
    pending: { color: 'bg-orange-100 text-orange-800 border-orange-200', label: 'Bekliyor' },
    completed: { color: 'bg-green-100 text-green-800 border-green-200', label: 'Tamamlandı' },
    draft: { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Taslak' }
  };
  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
  return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>{config.label}</span>;
};

// Helper function to get priority badge
const getPriorityBadge = (priority: string) => {
  const priorityConfig = {
    'Normal': { color: 'bg-blue-100 text-blue-800 border-blue-200', label: 'Normal' },
    'Acil': { color: 'bg-red-100 text-red-800 border-red-200', label: 'Acil' },
    'Çok Acil': { color: 'bg-red-200 text-red-900 border-red-300', label: 'Çok Acil' },
    'Düşük': { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Düşük' }
  };
  const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig['Normal'];
  return <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.color}`}>{config.label}</span>;
};


// Helper function to translate X-ray types to Turkish
const getXrayTypeLabel = (type: string) => {
  const typeLabels: Record<string, string> = {
    'periapikal': 'Periapikal Röntgen',
    'panoramik': 'Panoramik Röntgen',
    'bitewing': 'Bitewing Röntgen',
    'cbct': 'CBCT (3D Tomografi)',
    'sefalometrik': 'Sefalometrik',
    'tmj': 'TMJ Görüntüleme'
  };
  return typeLabels[type] || type;
};

export default function IstemFormuDetailsModal({ isOpen, onClose, form, patient, doctor }: IstemFormuDetailsModalProps) {
  if (!isOpen || !form) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      <div className="fixed inset-0 bg-gray-900/60 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={onClose} />
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-gray-50 rounded-2xl shadow-2xl transform transition-all animate-slideUp">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-t-2xl px-6 py-4 sticky top-0 z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold text-white">İstem Formu Detayları</h2>
                  <p className="text-blue-100 text-sm">Form #{form.id?.slice(-8)}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="w-9 h-9 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors cursor-pointer"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-5">
            {/* Patient and Doctor Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              {patient && (
                <Section icon={<UserIcon className="w-5 h-5" />} title="Hasta Bilgileri">
                  <InfoItem label="Adı Soyadı" value={`${patient.firstName} ${patient.lastName}`} />
                  <InfoItem label="TC Kimlik" value={patient.tcKimlik} />
                  <InfoItem label="Telefon" value={patient.phone} />
                  {patient.email && <InfoItem label="Email" value={patient.email} />}
                </Section>
              )}

              {doctor && (
                <Section icon={<UserIcon className="w-5 h-5" />} title="Doktor Bilgileri">
                  <InfoItem label="Adı Soyadı" value={`${doctor.firstName} ${doctor.lastName}`} />
                  <InfoItem label="Email" value={doctor.email} />
                </Section>
              )}
            </div>
            
            <Section icon={<AlertCircle className="w-5 h-5" />} title="Form Detayları">
                <InfoItem label="Durum" value={getStatusBadge(form.status)} />
                <InfoItem label="Öncelik" value={getPriorityBadge(form.priorityStatus)} />
                <InfoItem label="Oluşturulma Tarihi" value={formatDate(form.createdAt)} />
                <InfoItem label="Ödeme Sorumlusu" value={form.paymentResponsible === 'clinic' ? 'Klinik' : 'Hasta'} />
            </Section>

            <Section icon={<Camera className="w-5 h-5" />} title="Görüntüleme Detayları">
              {form.xrayTypes.length > 0 ? (
                <>
                  <InfoItem
                    label="Türler"
                    value={(
                      <div className="flex flex-wrap gap-1 justify-end">
                        {form.xrayTypes.map((type, index) => (
                          <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                            {getXrayTypeLabel(type)}
                          </span>
                        ))}
                      </div>
                    )}
                  />
                  {/* Visual full teeth set highlighting selected teeth */}
                  {form.selectedTeeth && (
                    <div className="mt-4 flex justify-center">
                      <FullTeethSet
                        selectedTeeth={form.selectedTeeth.split(', ')}
                        onTeethSelect={() => {}}
                        selectableTeeth={form.selectedTeeth.split(', ')}
                        className="scale-75"
                      />
                    </div>
                  )}
                  {form.bitewingSides && form.bitewingSides.length > 0 && (
                      <InfoItem
                        label="Bitewing Yönü"
                        value={(
                          <div className="flex flex-wrap gap-1 justify-end">
                            {form.bitewingSides.map((side, index) => (
                              <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                {side === 'left' ? 'Sol' : 'Sağ'}
                              </span>
                            ))}
                          </div>
                        )}
                      />
                  )}
                </>
              ) : (
                <p className="text-sm text-gray-500 text-center py-2">Görüntüleme türü seçilmemiş.</p>
              )}
            </Section>

            <Section icon={<FileText className="w-5 h-5" />} title="Teşhis ve Notlar">
                <div className="space-y-4">
                    <div>
                        <h4 className="font-semibold text-gray-700 mb-1">Teşhis</h4>
                        <p className="text-sm text-gray-600 whitespace-pre-wrap bg-gray-100 p-3 rounded-lg">
                          {form.diagnosis || 'Teşhis bilgisi girilmemiş.'}
                        </p>
                    </div>
                    {form.notes && (
                       <div>
                            <h4 className="font-semibold text-gray-700 mb-1">Notlar</h4>
                            <p className="text-sm text-gray-600 whitespace-pre-wrap bg-gray-100 p-3 rounded-lg">
                                {form.notes}
                            </p>
                       </div>
                    )}
                </div>
            </Section>

            {form.images && form.images.length > 0 && (
              <Section icon={<Camera className="w-5 h-5" />} title="Yüklenen Görüntüler">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {form.images.map((image, index) => (
                    <a href={image} target="_blank" rel="noopener noreferrer" key={index} className="group relative">
                      <Image 
                        src={image} 
                        alt={`Görüntü ${index + 1}`}
                        width={200}
                        height={112}
                        className="w-full h-28 object-cover rounded-lg border border-gray-200 group-hover:opacity-80 transition-opacity"
                      />
                       <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-lg">
                        <span className="text-white text-xs font-medium">Görüntüle</span>
                      </div>
                    </a>
                  ))}
                </div>
              </Section>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 