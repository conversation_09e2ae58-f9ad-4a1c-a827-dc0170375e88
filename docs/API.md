# API Documentation

This document describes the API structure and endpoints for the Dento application.

## 🏗️ Architecture Overview

The Dento application uses Firebase as the backend, providing:
- **Authentication**: Firebase Auth
- **Database**: Firestore
- **Storage**: Firebase Storage
- **Functions**: Firebase Cloud Functions
- **Real-time**: Firestore real-time listeners

## 🔐 Authentication

### Authentication Flow
1. User registers/logs in through Firebase Auth
2. Custom claims are set for role-based access
3. JWT tokens are used for API authentication
4. Role-based access control is enforced

### User Roles
- **doctor**: Full access to patient management and forms
- **assistant**: Limited access to patient data
- **admin**: System administration capabilities

## 📊 Data Models

### User
```typescript
interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address: string
  birthDate: Date
  role: 'doctor' | 'assistant'
  clinic: string
  createdAt: Date
  updatedAt: Date
}
```

### Patient
```typescript
interface Patient {
  id: string
  doctorId: string
  firstName: string
  lastName: string
  email?: string
  tcKimlik: string
  phone: string
  gender: string
  birthDate: Date
  yas?: number
  address?: string
  totalProcedures?: number
  createdAt: Date
  updatedAt: Date
}
```

### Request Form (İstem Formu)
```typescript
interface IstemFormuData {
  id?: string
  patientId: string
  userId: string
  diagnosis: string
  notes: string
  images?: string[]
  xrayTypes: string[]
  bitewingSides: string[]
  selectedTeeth?: string
  paymentResponsible: 'clinic' | 'patient'
  priorityStatus: 'Normal' | 'Acil' | 'Çok Acil' | 'Düşük'
  status: 'pending' | 'completed' | 'draft'
  createdAt: Date
  completedAt?: Date
}
```

### Pending User
```typescript
interface PendingUser {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Date
  updatedAt: Date
}
```

## 🔥 Firestore Collections

### `/users`
- **Purpose**: Store user profile data
- **Security**: Users can read/write their own data, admins can read all
- **Indexes**: email, role, clinic

### `/patients`
- **Purpose**: Store patient information
- **Security**: Doctors can read/write their patients, assistants read-only
- **Indexes**: doctorId, tcKimlik, createdAt

### `/istemFormu`
- **Purpose**: Store request forms
- **Security**: Users can read/write their own forms
- **Indexes**: patientId, userId, status, createdAt

### `/pendingUsers`
- **Purpose**: Store pending user registrations
- **Security**: Admins only
- **Indexes**: status, createdAt

## ⚡ Firebase Cloud Functions

### Authentication Functions

#### `processUserRegistration`
- **Trigger**: User creation
- **Purpose**: Send welcome email and set initial claims
- **Parameters**: User data
- **Returns**: Success/error status

#### `setAdminClaims`
- **Trigger**: HTTP callable
- **Purpose**: Set admin role for users
- **Parameters**: `{ uid: string }`
- **Returns**: Success/error status
- **Security**: Admin only

### Email Functions

#### `sendWelcomeEmail`
- **Trigger**: HTTP callable
- **Purpose**: Send welcome email to new users
- **Parameters**: `{ email: string, name: string }`
- **Returns**: Success/error status

#### `sendNotificationEmail`
- **Trigger**: HTTP callable
- **Purpose**: Send notification emails
- **Parameters**: `{ to: string, subject: string, content: string }`
- **Returns**: Success/error status

### Data Processing Functions

#### `updatePatientStats`
- **Trigger**: Firestore write on `/istemFormu`
- **Purpose**: Update patient procedure counts
- **Parameters**: Form data
- **Returns**: Updated stats

## 🛠️ Service Layer

### Authentication Service (`lib/services/userService.ts`)

```typescript
// Get current user profile
getCurrentUser(): Promise<User | null>

// Update user profile
updateUserProfile(data: Partial<User>): Promise<void>

// Change password
changePassword(currentPassword: string, newPassword: string): Promise<void>
```

### Patient Service (`lib/services/patientService.ts`)

```typescript
// Get all patients for current doctor
getPatients(): Promise<Patient[]>

// Get single patient
getPatient(id: string): Promise<Patient | null>

// Create new patient
createPatient(data: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>

// Update patient
updatePatient(id: string, data: Partial<Patient>): Promise<void>

// Delete patient
deletePatient(id: string): Promise<void>

// Get patient statistics
getPatientStats(): Promise<PatientStats>
```

### Request Form Service (`lib/services/istemFormuService.ts`)

```typescript
// Get all forms
getForms(): Promise<IstemFormuData[]>

// Get single form
getForm(id: string): Promise<IstemFormuData | null>

// Create new form
createForm(data: Omit<IstemFormuData, 'id' | 'createdAt'>): Promise<string>

// Update form
updateForm(id: string, data: Partial<IstemFormuData>): Promise<void>

// Delete form
deleteForm(id: string): Promise<void>

// Get form statistics
getFormStats(): Promise<FormStats>
```

### Admin Service (`lib/services/adminService.ts`)

```typescript
// Get all users
getAllUsers(): Promise<User[]>

// Get pending users
getPendingUsers(): Promise<PendingUser[]>

// Approve user
approveUser(id: string): Promise<void>

// Reject user
rejectUser(id: string): Promise<void>

// Set user role
setUserRole(uid: string, role: string): Promise<void>
```

### Storage Service (`lib/services/storageService.ts`)

```typescript
// Upload file
uploadFile(file: File, path: string): Promise<string>

// Delete file
deleteFile(path: string): Promise<void>

// Get download URL
getDownloadURL(path: string): Promise<string>
```

## 🔒 Security Rules

### Firestore Security Rules

```javascript
// Users collection
match /users/{userId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
  allow read: if request.auth != null && 
    request.auth.token.admin == true;
}

// Patients collection
match /patients/{patientId} {
  allow read, write: if request.auth != null && 
    resource.data.doctorId == request.auth.uid;
  allow read: if request.auth != null && 
    request.auth.token.role == 'assistant';
}

// Request forms collection
match /istemFormu/{formId} {
  allow read, write: if request.auth != null && 
    resource.data.userId == request.auth.uid;
}

// Pending users collection
match /pendingUsers/{userId} {
  allow read, write: if request.auth != null && 
    request.auth.token.admin == true;
}
```

### Storage Security Rules

```javascript
// Images
match /images/{allPaths=**} {
  allow read, write: if request.auth != null;
}

// Documents
match /documents/{allPaths=**} {
  allow read, write: if request.auth != null;
}
```

## 📡 Real-time Updates

### Firestore Listeners

The application uses Firestore real-time listeners for:
- Patient list updates
- Request form status changes
- User profile changes
- Admin notifications

### Implementation Example

```typescript
// Listen to patient updates
const unsubscribe = onSnapshot(
  collection(db, 'patients'),
  (snapshot) => {
    const patients = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))
    setPatients(patients)
  }
)
```

## 🚨 Error Handling

### Error Types
- **Authentication Errors**: Invalid credentials, expired tokens
- **Permission Errors**: Insufficient permissions
- **Validation Errors**: Invalid data format
- **Network Errors**: Connection issues
- **Server Errors**: Firebase function errors

### Error Response Format
```typescript
interface ApiError {
  code: string
  message: string
  details?: any
}
```

## 📊 Rate Limiting

Firebase automatically handles rate limiting for:
- Authentication requests
- Firestore operations
- Cloud Function calls
- Storage operations

## 🔍 Monitoring and Logging

### Firebase Analytics
- User engagement tracking
- Feature usage analytics
- Performance monitoring

### Error Tracking
- Firebase Crashlytics
- Function error logging
- Client-side error reporting

## 🧪 Testing

### API Testing
- Use Firebase Emulators for local testing
- Mock Firebase services in unit tests
- Integration tests with test data

### Example Test
```typescript
import { render, screen } from '@testing-library/react'
import { PatientService } from '@/lib/services/patientService'

// Mock Firebase
jest.mock('@/lib/firebase')

describe('PatientService', () => {
  it('should fetch patients', async () => {
    const patients = await PatientService.getPatients()
    expect(patients).toBeDefined()
  })
})
```

## 📚 Additional Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Firebase Cloud Functions](https://firebase.google.com/docs/functions)
- [Firebase Storage](https://firebase.google.com/docs/storage)
