# Deployment Guide

This guide covers the deployment process for the Dento application to various environments.

## 🏗️ Deployment Architecture

The Dento application consists of:
- **Frontend**: Next.js application (deployed to Vercel/Firebase Hosting)
- **Backend**: Firebase services (Auth, Firestore, Functions, Storage)
- **Email**: Brevo (Sendinblue) service

## 🌍 Environments

### Development
- **Purpose**: Local development and testing
- **URL**: http://localhost:3000
- **Firebase Project**: dento-dev
- **Database**: Development Firestore instance

### Staging
- **Purpose**: Pre-production testing
- **URL**: https://dento-staging.web.app
- **Firebase Project**: dento-staging
- **Database**: Staging Firestore instance

### Production
- **Purpose**: Live application
- **URL**: https://dento.web.app
- **Firebase Project**: dento-production
- **Database**: Production Firestore instance

## 🔧 Prerequisites

### Required Tools
- Node.js 18+
- Firebase CLI: `npm install -g firebase-tools`
- Git
- Access to Firebase projects

### Authentication
```bash
# Login to Firebase
firebase login

# Verify access to projects
firebase projects:list
```

## 🚀 Deployment Process

### 1. Pre-deployment Checklist

- [ ] All tests pass: `npm test`
- [ ] Code linting passes: `npm run lint`
- [ ] Build succeeds: `npm run build`
- [ ] Environment variables are configured
- [ ] Firebase security rules are updated
- [ ] Database migrations are ready (if any)

### 2. Environment Configuration

#### Development
```bash
firebase use dento-dev
```

#### Staging
```bash
firebase use dento-staging
```

#### Production
```bash
firebase use dento-production
```

### 3. Frontend Deployment

#### Option A: Firebase Hosting
```bash
# Build the application
npm run build

# Deploy to Firebase Hosting
firebase deploy --only hosting
```

#### Option B: Vercel (Alternative)
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel --prod
```

### 4. Backend Deployment

#### Firebase Functions
```bash
# Deploy functions
firebase deploy --only functions

# Deploy specific function
firebase deploy --only functions:functionName
```

#### Firestore Rules
```bash
# Deploy security rules
firebase deploy --only firestore:rules

# Deploy indexes
firebase deploy --only firestore:indexes
```

#### Storage Rules
```bash
# Deploy storage rules
firebase deploy --only storage
```

### 5. Complete Deployment
```bash
# Deploy everything
firebase deploy

# Deploy with specific message
firebase deploy -m "Release v1.2.3"
```

## 🔄 CI/CD Pipeline

### GitHub Actions (Recommended)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Firebase

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Run linting
      run: npm run lint
    
    - name: Build application
      run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to Firebase
      uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
        projectId: dento-production
```

### Manual Deployment Script

Create `scripts/deploy.sh`:

```bash
#!/bin/bash

# Deployment script for Dento application

set -e

echo "🚀 Starting deployment process..."

# Check if environment is specified
if [ -z "$1" ]; then
    echo "❌ Please specify environment: dev, staging, or production"
    exit 1
fi

ENVIRONMENT=$1

# Set Firebase project
case $ENVIRONMENT in
    dev)
        firebase use dento-dev
        ;;
    staging)
        firebase use dento-staging
        ;;
    production)
        firebase use dento-production
        ;;
    *)
        echo "❌ Invalid environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo "📦 Installing dependencies..."
npm ci

echo "🧪 Running tests..."
npm test

echo "🔍 Running linting..."
npm run lint

echo "🏗️ Building application..."
npm run build

echo "🚀 Deploying to $ENVIRONMENT..."
firebase deploy

echo "✅ Deployment completed successfully!"
```

Make it executable:
```bash
chmod +x scripts/deploy.sh
```

Usage:
```bash
./scripts/deploy.sh production
```

## 🔒 Environment Variables

### Development (.env.local)
```bash
NEXT_PUBLIC_FIREBASE_API_KEY=dev_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=dento-dev.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=dento-dev
# ... other dev variables
```

### Production
Set environment variables in:
- **Vercel**: Project settings → Environment Variables
- **Firebase Hosting**: Firebase console → Hosting → Environment configuration

## 📊 Monitoring and Health Checks

### Post-deployment Verification

1. **Frontend Health Check**
   - Verify application loads
   - Test authentication flow
   - Check critical user paths

2. **Backend Health Check**
   - Test Firebase Functions
   - Verify database connections
   - Check email service integration

3. **Performance Monitoring**
   - Monitor Core Web Vitals
   - Check Firebase performance
   - Monitor error rates

### Monitoring Tools

- **Firebase Console**: Real-time monitoring
- **Google Analytics**: User behavior tracking
- **Firebase Performance**: Performance metrics
- **Firebase Crashlytics**: Error tracking

## 🔄 Rollback Procedures

### Frontend Rollback
```bash
# Rollback to previous version
firebase hosting:clone SOURCE_SITE_ID:SOURCE_VERSION_ID TARGET_SITE_ID
```

### Functions Rollback
```bash
# List function versions
firebase functions:log

# Rollback specific function
firebase deploy --only functions:functionName
```

### Database Rollback
- Use Firestore backup/restore
- Apply reverse migrations if needed

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Clear node_modules and reinstall
   - Verify environment variables

2. **Function Deployment Errors**
   - Check function syntax
   - Verify dependencies in functions/package.json
   - Check Firebase quotas

3. **Permission Errors**
   - Verify Firebase project access
   - Check IAM roles
   - Ensure service account permissions

### Debug Commands
```bash
# Check Firebase project status
firebase projects:list

# View function logs
firebase functions:log

# Check hosting status
firebase hosting:sites:list

# Test functions locally
firebase emulators:start --only functions
```

## 📋 Deployment Checklist

### Pre-deployment
- [ ] Code review completed
- [ ] Tests passing
- [ ] Security review completed
- [ ] Performance testing done
- [ ] Documentation updated

### During Deployment
- [ ] Backup current version
- [ ] Deploy to staging first
- [ ] Run smoke tests
- [ ] Monitor error rates
- [ ] Verify critical functionality

### Post-deployment
- [ ] Health checks passed
- [ ] Performance metrics normal
- [ ] User acceptance testing
- [ ] Monitor for 24 hours
- [ ] Update deployment documentation

## 📚 Additional Resources

- [Firebase Hosting Documentation](https://firebase.google.com/docs/hosting)
- [Firebase Functions Deployment](https://firebase.google.com/docs/functions/manage-functions)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [Vercel Deployment Documentation](https://vercel.com/docs)
