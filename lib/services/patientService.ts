import { db } from '../firebase';
import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  getCountFromServer,
  QueryConstraint,
} from 'firebase/firestore';
import { Patient } from '../types';

const patientsCollection = collection(db, 'patients');

// Helper function to calculate age
const calculateAge = (birthDate: Date): number => {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDifference = today.getMonth() - birthDate.getMonth();
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};

// Create a new patient
export const addPatient = async (patientData: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const age = patientData.birthDate ? calculateAge(patientData.birthDate) : undefined;

    // Business logic: Set default values for new patients
    const processedData = {
      ...patientData,
      totalProcedures: patientData.totalProcedures || 0, // Default to 0 if not provided
      yas: age,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const docRef = await addDoc(patientsCollection, processedData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding patient: ', error);
    throw new Error('Failed to add patient');
  }
};

// Get all patients for a doctor
export const getPatientsByDoctorId = async (doctorId: string): Promise<Patient[]> => {
  try {
    const q = query(patientsCollection, where('doctorId', '==', doctorId));
    const querySnapshot = await getDocs(q);
    const patients: Patient[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      patients.push({
        id: doc.id,
        ...data,
        birthDate: data.birthDate.toDate(),
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      } as Patient);
    });
    return patients;
  } catch (error) {
    console.error('Error getting patients: ', error);
    throw new Error('Failed to get patients');
  }
};

// Get a single patient by ID
export const getPatientById = async (patientId: string): Promise<Patient | null> => {
  try {
    const docRef = doc(db, 'patients', patientId);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      const data = docSnap.data();
      return {
        id: docSnap.id,
        ...data,
        birthDate: data.birthDate.toDate(),
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      } as Patient;
    } else {
      console.log('No such patient!');
      return null;
    }
  } catch (error) {
    console.error('Error getting patient: ', error);
    throw new Error('Failed to get patient');
  }
};

// Update a patient's information
export const updatePatient = async (patientId: string, updatedData: Partial<Patient>): Promise<void> => {
  try {
    const age = updatedData.birthDate ? calculateAge(new Date(updatedData.birthDate)) : undefined;
    const docRef = doc(db, 'patients', patientId);
    await updateDoc(docRef, {
      ...updatedData,
      yas: age,
      updatedAt: new Date(),
    });
  } catch (error) {
    console.error('Error updating patient: ', error);
    throw new Error('Failed to update patient');
  }
};

// Delete a patient
export const deletePatient = async (patientId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'patients', patientId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting patient: ', error);
    throw new Error('Failed to delete patient');
  }
};

// Get patients for a doctor with pagination and search
export interface GetPatientsPaginatedParams {
  doctorId: string;
  page?: number;            // 1-indexed
  pageSize?: number;        // defaults to 10
  searchQuery?: string;     // Search term for patient's name, phone, or TC kimlik (case-insensitive, contains)
}

export interface PaginatedPatientsResult {
  patients: Patient[];
  total: number;            // total patients that match the criteria (without pagination)
}

export const getPatientsPaginated = async (params: GetPatientsPaginatedParams): Promise<PaginatedPatientsResult> => {
  const {
    doctorId,
    page = 1,
    pageSize = 10,
    searchQuery,
  } = params;

  try {
    // Base query filters (doctorId only)
    const constraints: QueryConstraint[] = [where('doctorId', '==', doctorId)];


    // To ensure deterministic ordering when paginating, we order by createdAt descending.

    const baseQuery = query(patientsCollection, ...constraints);

    // Get total count efficiently using aggregation query
    const countSnap = await getCountFromServer(baseQuery);
    const total = countSnap.data().count;

    const pageQuery = query(
      baseQuery,
      orderBy('createdAt', 'desc'),
      limit(page * pageSize),
    );

    const querySnapshot = await getDocs(pageQuery);

    let patients: Patient[] = [];
    // Slice the last `pageSize` documents corresponding to the requested page
    const pageDocs = querySnapshot.docs.slice((page - 1) * pageSize, page * pageSize);

    pageDocs.forEach((docSnap) => {
      const data = docSnap.data();
      const patient: Patient = {
        id: docSnap.id,
        ...data,
        birthDate: data.birthDate?.toDate ? data.birthDate.toDate() : data.birthDate,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      } as Patient;
      patients.push(patient);
    });

    // Client-side search (case-insensitive, contains) - name, phone, or TC kimlik
    if (searchQuery) {
      const lower = searchQuery.toLowerCase();
      patients = patients.filter(p =>
        p.firstName.toLowerCase().includes(lower) ||
        p.lastName.toLowerCase().includes(lower) ||
        p.phone.toLowerCase().includes(lower) ||
        p.tcKimlik.toLowerCase().includes(lower)
      );
    }

    return { patients, total };
  } catch (error) {
    console.error('Error getting paginated patients: ', error);
    throw new Error('Failed to get paginated patients');
  }
}; 