/**
 * Main utilities export file
 * Re-exports all utility functions from organized modules
 *
 * This file maintains backward compatibility while providing
 * a cleaner, more organized structure for utility functions.
 */

// Class name utilities
export { cn } from './utils/cn'

// Date utilities
export {
  formatDate,
  formatDateTime,
  formatDateOnly,
  calculateAge,
  isToday,
  isPast,
  getRelativeTime,
} from './utils/date'

// Firebase utilities
export {
  convertTimestamps,
  convertDatesToTimestamps,
  extractDocumentData,
  convertDocuments,
  cleanFirestoreData,
} from './utils/firebase'

// Error handling utilities
export {
  getFirebaseAuthErrorTurkish,
  getFirestoreErrorTurkish,
  getErrorMessage,
  logError,
  createError,
} from './utils/errors'

// Validation utilities
export {
  isValidTCKimlik,
  isValidEmail,
  isValidTurkishPhone,
  validatePassword,
  isValidAge,
  isValidName,
  validateFile,
  sanitizeText,
  isValidTurkishPostalCode,
} from './utils/validation'

// Formatting utilities
export {
  formatTurkishPhone,
  formatTCKimlik,
  formatName,
  formatFileSize,
  formatNumber,
  formatCurrency,
  formatPercentage,
  truncateText,
  formatAddress,
  formatPriority,
  formatStatus,
} from './utils/format'
