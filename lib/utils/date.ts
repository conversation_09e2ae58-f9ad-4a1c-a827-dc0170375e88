/**
 * Date formatting utilities for the Dento application
 * Provides consistent date formatting across the application
 */

/**
 * Formats a date to Turkish locale short format
 * @param date - Date to format
 * @returns Formatted date string or '-' if date is null/undefined
 * 
 * @example
 * formatDate(new Date()) // "15 Oca 2024"
 */
export const formatDate = (date: Date | undefined | null): string => {
  if (!date) return '-';
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

/**
 * Formats a date with time to Turkish locale format
 * @param date - Date to format
 * @returns Formatted date and time string or '-' if date is null/undefined
 * 
 * @example
 * formatDateTime(new Date()) // "15.01.2024 14:30"
 */
export const formatDateTime = (date: Date | undefined | null): string => {
  if (!date) return '-';
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

/**
 * Formats a date to Turkish locale date only format
 * @param date - Date to format
 * @returns Formatted date string or 'Belirtilmemiş' if date is null/undefined
 * 
 * @example
 * formatDateOnly(new Date()) // "15.01.2024"
 */
export const formatDateOnly = (date: Date | undefined | null): string => {
  if (!date) return 'Belirtilmemiş';
  return new Intl.DateTimeFormat('tr-TR').format(date);
};

/**
 * Calculates age from birth date
 * @param birthDate - Birth date
 * @returns Age in years
 * 
 * @example
 * calculateAge(new Date('1990-01-15')) // 34 (if current year is 2024)
 */
export const calculateAge = (birthDate: Date): number => {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Checks if a date is today
 * @param date - Date to check
 * @returns True if date is today
 */
export const isToday = (date: Date): boolean => {
  const today = new Date();
  return date.toDateString() === today.toDateString();
};

/**
 * Checks if a date is in the past
 * @param date - Date to check
 * @returns True if date is in the past
 */
export const isPast = (date: Date): boolean => {
  return date < new Date();
};

/**
 * Gets relative time string (e.g., "2 hours ago", "in 3 days")
 * @param date - Date to compare
 * @returns Relative time string in Turkish
 */
export const getRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Az önce';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} dakika önce`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} saat önce`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} gün önce`;
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} ay önce`;
  }
  
  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} yıl önce`;
};
