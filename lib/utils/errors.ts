/**
 * Error handling utilities for the Dento application
 * Provides user-friendly error messages in Turkish
 */

/**
 * Converts Firebase Auth error codes to Turkish error messages
 * @param errorCode - Firebase Auth error code
 * @returns User-friendly error message in Turkish
 * 
 * @example
 * getFirebaseAuthErrorTurkish('auth/invalid-email') // "Geçersiz e-posta adresi..."
 */
export const getFirebaseAuthErrorTurkish = (errorCode: string): string => {
  switch (errorCode) {
    case 'auth/invalid-email':
      return 'Geçersiz e-posta adresi. Lütfen kontrol edip tekrar deneyin.';
    case 'auth/user-disabled':
      return 'Bu kullanıcı hesabı devre dışı bırakılmıştır.';
    case 'auth/user-not-found':
      return 'Bu e-posta adresi ile kayıtlı bir kullanıcı bulunamadı.';
    case 'auth/wrong-password':
      return 'Hatalı şifre. Lütfen şifrenizi kontrol edip tekrar deneyin.';
    case 'auth/email-already-in-use':
      return 'Bu e-posta adresi zaten başka bir hesap tarafından kullanılıyor.';
    case 'auth/operation-not-allowed':
      return 'Bu işlem şu anda etkin değil. Lütfen daha sonra tekrar deneyin.';
    case 'auth/weak-password':
      return 'Şifre çok zayıf. Lütfen daha güçlü bir şifre seçin.';
    case 'auth/too-many-requests':
      return 'Çok fazla başarısız deneme. Lütfen daha sonra tekrar deneyin.';
    case 'auth/network-request-failed':
      return 'Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.';
    case 'auth/invalid-credential':
      return 'Geçersiz kimlik bilgileri. Lütfen bilgilerinizi kontrol edin.';
    case 'auth/requires-recent-login':
      return 'Bu işlem için yeniden giriş yapmanız gerekiyor.';
    default:
      return 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
  }
};

/**
 * Converts Firestore error codes to Turkish error messages
 * @param errorCode - Firestore error code
 * @returns User-friendly error message in Turkish
 */
export const getFirestoreErrorTurkish = (errorCode: string): string => {
  switch (errorCode) {
    case 'permission-denied':
      return 'Bu işlem için yetkiniz bulunmuyor.';
    case 'not-found':
      return 'Aranan veri bulunamadı.';
    case 'already-exists':
      return 'Bu veri zaten mevcut.';
    case 'resource-exhausted':
      return 'Sistem kapasitesi aşıldı. Lütfen daha sonra tekrar deneyin.';
    case 'failed-precondition':
      return 'İşlem gereksinimleri karşılanmadı.';
    case 'aborted':
      return 'İşlem iptal edildi. Lütfen tekrar deneyin.';
    case 'out-of-range':
      return 'Geçersiz veri aralığı.';
    case 'unimplemented':
      return 'Bu özellik henüz desteklenmiyor.';
    case 'internal':
      return 'Sistem hatası oluştu. Lütfen daha sonra tekrar deneyin.';
    case 'unavailable':
      return 'Servis şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.';
    case 'data-loss':
      return 'Veri kaybı oluştu. Lütfen destek ekibi ile iletişime geçin.';
    default:
      return 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
  }
};

/**
 * Generic error handler that determines error type and returns appropriate message
 * @param error - Error object
 * @returns User-friendly error message in Turkish
 */
export const getErrorMessage = (error: any): string => {
  if (error?.code) {
    // Firebase Auth errors
    if (error.code.startsWith('auth/')) {
      return getFirebaseAuthErrorTurkish(error.code);
    }
    // Firestore errors
    if (error.code.includes('/')) {
      return getFirestoreErrorTurkish(error.code);
    }
  }
  
  // Network errors
  if (error?.message?.includes('network') || error?.message?.includes('fetch')) {
    return 'Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.';
  }
  
  // Validation errors
  if (error?.name === 'ValidationError' || error?.message?.includes('validation')) {
    return 'Girilen bilgiler geçersiz. Lütfen kontrol edip tekrar deneyin.';
  }
  
  // Default error message
  return error?.message || 'Beklenmeyen bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
};

/**
 * Logs error to console in development and to external service in production
 * @param error - Error to log
 * @param context - Additional context information
 */
export const logError = (error: any, context?: string) => {
  const errorInfo = {
    message: error?.message || 'Unknown error',
    code: error?.code,
    stack: error?.stack,
    context,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Server',
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.error('Error logged:', errorInfo);
  } else {
    // In production, send to error tracking service
    // Example: Sentry, LogRocket, etc.
    console.error('Production error:', errorInfo);
  }
};

/**
 * Creates a standardized error response
 * @param message - Error message
 * @param code - Error code
 * @param details - Additional error details
 * @returns Standardized error object
 */
export const createError = (message: string, code?: string, details?: any) => {
  return {
    error: true,
    message,
    code,
    details,
    timestamp: new Date().toISOString(),
  };
};
