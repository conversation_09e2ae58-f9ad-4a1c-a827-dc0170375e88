import { DocumentData, Timestamp } from "firebase/firestore";

/**
 * Firebase utility functions for data conversion and error handling
 */

/**
 * Converts Firestore timestamps to Date objects recursively
 * @param data - Document data from Firestore
 * @returns Data with timestamps converted to Date objects
 * 
 * @example
 * const userData = convertTimestamps(doc.data())
 */
export const convertTimestamps = (data: DocumentData) => {
  const converted = { ...data };
  Object.keys(converted).forEach(key => {
    if (converted[key] instanceof Timestamp) {
      converted[key] = converted[key].toDate();
    }
  });
  return converted;
};

/**
 * Converts Date objects to Firestore timestamps for saving
 * @param data - Data object with Date fields
 * @returns Data with Date objects converted to Timestamps
 */
export const convertDatesToTimestamps = (data: any) => {
  const converted = { ...data };
  Object.keys(converted).forEach(key => {
    if (converted[key] instanceof Date) {
      converted[key] = Timestamp.fromDate(converted[key]);
    }
  });
  return converted;
};

/**
 * Safely extracts document data with ID
 * @param doc - Firestore document snapshot
 * @returns Document data with ID and converted timestamps
 */
export const extractDocumentData = (doc: any) => {
  if (!doc.exists()) {
    return null;
  }
  
  return {
    id: doc.id,
    ...convertTimestamps(doc.data())
  };
};

/**
 * Batch converts multiple documents
 * @param docs - Array of Firestore document snapshots
 * @returns Array of converted document data
 */
export const convertDocuments = (docs: any[]) => {
  return docs.map(doc => extractDocumentData(doc)).filter(Boolean);
};

/**
 * Creates a Firestore-safe object by removing undefined values
 * @param data - Object to clean
 * @returns Object without undefined values
 */
export const cleanFirestoreData = (data: any) => {
  const cleaned: any = {};
  Object.keys(data).forEach(key => {
    if (data[key] !== undefined) {
      cleaned[key] = data[key];
    }
  });
  return cleaned;
};
