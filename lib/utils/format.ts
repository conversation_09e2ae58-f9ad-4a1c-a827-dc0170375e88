/**
 * Formatting utilities for the Dento application
 * Provides consistent formatting for various data types
 */

/**
 * Formats a phone number to Turkish format
 * @param phone - Phone number to format
 * @returns Formatted phone number
 * 
 * @example
 * formatTurkishPhone('05551234567') // '0555 123 45 67'
 */
export const formatTurkishPhone = (phone: string): string => {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Format Turkish mobile number
  if (cleanPhone.length === 11 && cleanPhone.startsWith('05')) {
    return `${cleanPhone.slice(0, 4)} ${cleanPhone.slice(4, 7)} ${cleanPhone.slice(7, 9)} ${cleanPhone.slice(9)}`;
  }
  
  // Format international number
  if (cleanPhone.length === 13 && cleanPhone.startsWith('905')) {
    return `+90 ${cleanPhone.slice(3, 6)} ${cleanPhone.slice(6, 9)} ${cleanPhone.slice(9, 11)} ${cleanPhone.slice(11)}`;
  }
  
  return phone; // Return original if no format matches
};

/**
 * Formats TC Kimlik number with spaces
 * @param tcKimlik - TC Kimlik number to format
 * @returns Formatted TC Kimlik number
 * 
 * @example
 * formatTCKimlik('12345678901') // '123 456 789 01'
 */
export const formatTCKimlik = (tcKimlik: string): string => {
  if (!tcKimlik || tcKimlik.length !== 11) return tcKimlik;
  
  return `${tcKimlik.slice(0, 3)} ${tcKimlik.slice(3, 6)} ${tcKimlik.slice(6, 9)} ${tcKimlik.slice(9)}`;
};

/**
 * Formats a name to proper case (first letter uppercase, rest lowercase)
 * @param name - Name to format
 * @returns Formatted name
 * 
 * @example
 * formatName('AHMET MEHMET') // 'Ahmet Mehmet'
 */
export const formatName = (name: string): string => {
  if (!name) return '';
  
  return name
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

/**
 * Formats file size to human readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size
 * 
 * @example
 * formatFileSize(1024) // '1 KB'
 * formatFileSize(1048576) // '1 MB'
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Formats a number with Turkish locale
 * @param number - Number to format
 * @param options - Intl.NumberFormat options
 * @returns Formatted number
 */
export const formatNumber = (
  number: number,
  options?: Intl.NumberFormatOptions
): string => {
  return new Intl.NumberFormat('tr-TR', options).format(number);
};

/**
 * Formats currency in Turkish Lira
 * @param amount - Amount to format
 * @returns Formatted currency
 * 
 * @example
 * formatCurrency(1234.56) // '₺1.234,56'
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
  }).format(amount);
};

/**
 * Formats percentage with Turkish locale
 * @param value - Value to format as percentage (0-1)
 * @returns Formatted percentage
 * 
 * @example
 * formatPercentage(0.1234) // '%12,34'
 */
export const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('tr-TR', {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value);
};

/**
 * Truncates text to specified length with ellipsis
 * @param text - Text to truncate
 * @param maxLength - Maximum length
 * @returns Truncated text
 * 
 * @example
 * truncateText('This is a long text', 10) // 'This is a...'
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) return text;
  
  return text.slice(0, maxLength).trim() + '...';
};

/**
 * Formats address to a single line
 * @param address - Address object or string
 * @returns Formatted address string
 */
export const formatAddress = (address: any): string => {
  if (typeof address === 'string') return address;
  
  if (typeof address === 'object') {
    const parts = [
      address.street,
      address.district,
      address.city,
      address.postalCode,
    ].filter(Boolean);
    
    return parts.join(', ');
  }
  
  return '';
};

/**
 * Formats priority status to display text
 * @param priority - Priority status
 * @returns Formatted priority text
 */
export const formatPriority = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    'Normal': 'Normal',
    'Acil': 'Acil',
    'Çok Acil': 'Çok Acil',
    'Düşük': 'Düşük Öncelik',
  };
  
  return priorityMap[priority] || priority;
};

/**
 * Formats status to display text with appropriate styling
 * @param status - Status value
 * @returns Object with formatted text and CSS class
 */
export const formatStatus = (status: string) => {
  const statusMap: Record<string, { text: string; className: string }> = {
    'pending': { text: 'Beklemede', className: 'text-yellow-600 bg-yellow-100' },
    'completed': { text: 'Tamamlandı', className: 'text-green-600 bg-green-100' },
    'draft': { text: 'Taslak', className: 'text-gray-600 bg-gray-100' },
    'approved': { text: 'Onaylandı', className: 'text-green-600 bg-green-100' },
    'rejected': { text: 'Reddedildi', className: 'text-red-600 bg-red-100' },
  };
  
  return statusMap[status] || { text: status, className: 'text-gray-600 bg-gray-100' };
};
