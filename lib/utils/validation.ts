/**
 * Validation utilities for the Dento application
 * Provides common validation functions for forms and data
 */

/**
 * Validates Turkish TC Kimlik number
 * @param tcKimlik - TC Kimlik number to validate
 * @returns True if valid TC Kimlik number
 * 
 * @example
 * isValidTCKimlik('12345678901') // true/false
 */
export const isValidTCKimlik = (tcKimlik: string): boolean => {
  if (!tcKimlik || tcKimlik.length !== 11) {
    return false;
  }
  
  // Check if all digits are the same
  if (/^(\d)\1{10}$/.test(tcKimlik)) {
    return false;
  }
  
  const digits = tcKimlik.split('').map(Number);
  
  // First digit cannot be 0
  if (digits[0] === 0) {
    return false;
  }
  
  // Calculate checksum
  const oddSum = digits[0] + digits[2] + digits[4] + digits[6] + digits[8];
  const evenSum = digits[1] + digits[3] + digits[5] + digits[7];
  
  const checkDigit1 = (oddSum * 7 - evenSum) % 10;
  const checkDigit2 = (oddSum + evenSum + digits[9]) % 10;
  
  return digits[9] === checkDigit1 && digits[10] === checkDigit2;
};

/**
 * Validates email address format
 * @param email - Email address to validate
 * @returns True if valid email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates Turkish phone number format
 * @param phone - Phone number to validate
 * @returns True if valid Turkish phone number
 * 
 * @example
 * isValidTurkishPhone('05551234567') // true
 * isValidTurkishPhone('+905551234567') // true
 */
export const isValidTurkishPhone = (phone: string): boolean => {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check for Turkish mobile format
  if (cleanPhone.length === 11 && cleanPhone.startsWith('05')) {
    return true;
  }
  
  // Check for international format
  if (cleanPhone.length === 13 && cleanPhone.startsWith('905')) {
    return true;
  }
  
  return false;
};

/**
 * Validates password strength
 * @param password - Password to validate
 * @returns Object with validation result and requirements
 */
export const validatePassword = (password: string) => {
  const requirements = {
    minLength: password.length >= 8,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };
  
  const isValid = Object.values(requirements).every(Boolean);
  
  return {
    isValid,
    requirements,
    score: Object.values(requirements).filter(Boolean).length,
  };
};

/**
 * Validates age is within acceptable range
 * @param birthDate - Birth date to validate
 * @param minAge - Minimum age (default: 0)
 * @param maxAge - Maximum age (default: 120)
 * @returns True if age is within range
 */
export const isValidAge = (birthDate: Date, minAge = 0, maxAge = 120): boolean => {
  const today = new Date();
  const birth = new Date(birthDate);
  
  if (birth > today) {
    return false; // Birth date cannot be in the future
  }
  
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age >= minAge && age <= maxAge;
};

/**
 * Validates that a string contains only letters and spaces
 * @param text - Text to validate
 * @returns True if text contains only letters and spaces
 */
export const isValidName = (text: string): boolean => {
  const nameRegex = /^[a-zA-ZçğıöşüÇĞIİÖŞÜ\s]+$/;
  return nameRegex.test(text.trim());
};

/**
 * Validates file type and size
 * @param file - File to validate
 * @param allowedTypes - Array of allowed MIME types
 * @param maxSizeMB - Maximum file size in MB
 * @returns Validation result
 */
export const validateFile = (
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/gif'],
  maxSizeMB = 5
) => {
  const errors: string[] = [];
  
  if (!allowedTypes.includes(file.type)) {
    errors.push(`Desteklenmeyen dosya türü: ${file.type}`);
  }
  
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    errors.push(`Dosya boyutu ${maxSizeMB}MB'dan büyük olamaz`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Sanitizes input text by removing potentially harmful characters
 * @param text - Text to sanitize
 * @returns Sanitized text
 */
export const sanitizeText = (text: string): string => {
  return text
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Validates Turkish postal code format
 * @param postalCode - Postal code to validate
 * @returns True if valid Turkish postal code
 */
export const isValidTurkishPostalCode = (postalCode: string): boolean => {
  const cleanCode = postalCode.replace(/\s/g, '');
  return /^\d{5}$/.test(cleanCode);
};
