rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Allow only authenticated users with admin custom claims to upload scan images
    match /scan-images/{userId}/{patientId}/{filename} {
      allow write: if request.auth != null && 
                   request.auth.token.admin == true;
      allow read: if request.auth != null;
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
