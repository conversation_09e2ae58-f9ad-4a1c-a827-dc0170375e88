// Mock Firebase services for testing

export const mockAuth = {
  currentUser: null,
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  signOut: jest.fn(),
  onAuthStateChanged: jest.fn(),
}

export const mockDb = {
  collection: jest.fn(() => ({
    doc: jest.fn(() => ({
      get: jest.fn(),
      set: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      onSnapshot: jest.fn(),
    })),
    add: jest.fn(),
    where: jest.fn(() => ({
      get: jest.fn(),
      onSnapshot: jest.fn(),
    })),
    orderBy: jest.fn(() => ({
      get: jest.fn(),
      onSnapshot: jest.fn(),
    })),
    limit: jest.fn(() => ({
      get: jest.fn(),
      onSnapshot: jest.fn(),
    })),
  })),
}

export const mockStorage = {
  ref: jest.fn(() => ({
    put: jest.fn(),
    getDownloadURL: jest.fn(),
    delete: jest.fn(),
  })),
}

// Mock Firebase app
export const mockFirebaseApp = {
  auth: () => mockAuth,
  firestore: () => mockDb,
  storage: () => mockStorage,
}
